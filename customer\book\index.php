<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../../config/app.php';
} catch (Exception $e) {
    die("Configuration Error: " . $e->getMessage());
}

try {
    require_once __DIR__ . '/../../includes/customer_panel_functions.php';
} catch (Exception $e) {
    die("Customer Panel Functions Error: " . $e->getMessage());
}

// Fix currency symbol if corrupted in database
try {
    $currentSymbol = getSetting('business', 'currency_symbol', null);
    if ($currentSymbol === '262145' || $currentSymbol === 262145) {
        setSetting('business', 'currency_symbol', 'TSH');
        error_log("Customer book: Fixed corrupted currency_symbol (was 262145, now TSH)");
    }
} catch (Exception $e) {
    error_log("Customer book: Error checking currency_symbol: " . $e->getMessage());
}

// Ensure we have the correct currency symbol for this page
$safeCurrencySymbol = 'TSH'; // Default fallback
try {
    $dbSymbol = getSetting('business', 'currency_symbol', null);
    if ($dbSymbol && $dbSymbol !== '262145' && $dbSymbol !== 262145) {
        $safeCurrencySymbol = $dbSymbol;
    } else {
        // Use constant if database is corrupted
        $safeCurrencySymbol = defined('CURRENCY_SYMBOL') && CURRENCY_SYMBOL !== '262145' ? CURRENCY_SYMBOL : 'TSH';
    }
} catch (Exception $e) {
    $safeCurrencySymbol = 'TSH';
}

// Override the constant if it's corrupted
if (!defined('CURRENCY_SYMBOL') || CURRENCY_SYMBOL === '262145') {
    if (defined('CURRENCY_SYMBOL')) {
        // Can't redefine, but we'll use our safe variable
        error_log("Customer book: CURRENCY_SYMBOL constant is corrupted (262145), using safe fallback");
    }
}

// Check if required functions exist
$requiredFunctions = ['createCustomerBooking', 'getPackageServices', 'isLoggedIn'];
foreach ($requiredFunctions as $func) {
    if (!function_exists($func)) {
        die("Error: Function '$func' not found. Please check includes.");
    }
}

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'create_booking') {
            $bookingData = [
                'service_id' => !empty($_POST['service_id']) ? $_POST['service_id'] : null,
                'service_variation_id' => !empty($_POST['service_variation_id']) ? $_POST['service_variation_id'] : null,
                'package_id' => !empty($_POST['package_id']) ? $_POST['package_id'] : null,
                'staff_id' => $_POST['staff_id'],
                'date' => $_POST['date'],
                'start_time' => $_POST['start_time'],
                'end_time' => $_POST['end_time'],
                'points_used' => intval($_POST['points_used'] ?? 0),
                'notes' => $_POST['notes'] ?? null
            ];

            $bookingId = createCustomerBooking($_SESSION['user_id'], $bookingData);

            // Send booking acknowledgment email
            $userId = $_SESSION['user_id'];
            $user = $database->fetch("SELECT * FROM users WHERE id = ?", [$userId]);

            if ($user) {
                // Get service/package details with variation support
                $serviceName = '';
                $duration = '';
                $price = '';

                if ($bookingData['service_id']) {
                    $service = $database->fetch("SELECT * FROM services WHERE id = ?", [$bookingData['service_id']]);
                    $serviceName = $service['name'];
                    $duration = $service['duration'];
                    $price = formatCurrency($service['price']);

                    // Check if there's a service variation
                    if (!empty($bookingData['service_variation_id'])) {
                        $variation = $database->fetch("SELECT * FROM service_variations WHERE id = ?", [$bookingData['service_variation_id']]);
                        if ($variation) {
                            $serviceName .= ' (' . $variation['name'] . ')';
                            $duration = $variation['duration'];
                            $price = formatCurrency($variation['price']);
                        }
                    }
                } else if ($bookingData['package_id']) {
                    $package = $database->fetch("SELECT * FROM packages WHERE id = ?", [$bookingData['package_id']]);
                    $serviceName = $package['name'];
                    
                    // Use package_duration if set, otherwise calculate from services
                    if ($package['package_duration'] > 0) {
                        $duration = $package['package_duration'];
                    } else {
                        // Calculate duration from package services
                        $packageServices = getPackageServices($package['id']);
                        $duration = array_sum(array_column($packageServices, 'duration'));
                    }
                    
                    $price = formatCurrency($package['price']);
                }

                // Get staff details
                $staff = $database->fetch("SELECT * FROM users WHERE id = ?", [$bookingData['staff_id']]);
                $staffName = $staff ? $staff['name'] : 'Selected Staff';

                $subject = "Booking Request Received - " . APP_NAME;
                
                $body = '
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                            <h1 style="margin: 0; font-size: 28px;">' . APP_NAME . '</h1>
                            <p style="margin: 10px 0 0 0; opacity: 0.9;">Booking Request Received</p>
                        </div>
                        
                        <div style="padding: 30px; background: #f8f9fa;">
                            <h2 style="color: #333; margin-top: 0;">Dear ' . htmlspecialchars($user['name']) . ',</h2>
                            <p style="color: #666; line-height: 1.6;">Thank you for choosing ' . APP_NAME . '. We have received your booking request and our team will review it shortly.</p>
                            
                            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                                <h3 style="margin-top: 0; color: #333;">Booking Details</h3>
                                <p><strong>Service:</strong> ' . htmlspecialchars($serviceName) . '</p>
                                <p><strong>Date:</strong> ' . formatDate($bookingData['date']) . '</p>
                                <p><strong>Time:</strong> ' . formatTime($bookingData['start_time']) . ' - ' . formatTime($bookingData['end_time']) . '</p>
                                <p><strong>Duration:</strong> ' . $duration . ' minutes</p>
                                <p><strong>Staff:</strong> ' . htmlspecialchars($staffName) . '</p>
                                ' . (shouldShowPricing() ? '<p><strong>Price:</strong> ' . $price . '</p>' : '') . '
                            </div>
                            
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                <p style="margin: 0; color: #1565c0;">
                                    <strong>What\'s Next?</strong><br>
                                    1. Our team will review your booking request<br>
                                    2. You will receive a confirmation email once approved<br>
                                    3. You can view your booking status in your dashboard
                                </p>
                            </div>
                            
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="' . getBaseUrl() . '/customer/bookings" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View My Bookings</a>
                            </div>
                            
                            <p style="color: #666; line-height: 1.6;">If you have any questions or need to make changes to your booking, please don\'t hesitate to contact us.</p>
                        </div>
                        
                        <div style="background: #333; color: white; padding: 20px; text-align: center;">
                            <p style="margin: 0;">' . APP_NAME . ' | +255 123 456 789 | ' . SMTP_FROM_EMAIL . '</p>
                        </div>
                    </div>
                ';

                // Send the email
                sendSMTPEmail($user['email'], $subject, $body);
            }

            $message = 'Appointment booked successfully! Booking ID: ' . $bookingId;
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);

// Get services, packages, and staff
global $database;

// Get all categories and subcategories for filters
$allCategories = $database->fetchAll("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name");
$allSubcategories = $database->fetchAll("
    SELECT ss.*, sc.name as category_name 
    FROM service_subcategories ss 
    JOIN service_categories sc ON ss.category_id = sc.id 
    WHERE ss.is_active = 1 AND sc.is_active = 1 
    ORDER BY sc.name, ss.sort_order, ss.name
");

// Pagination settings for services
$servicesPerPage = 10;
$currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($currentPage - 1) * $servicesPerPage;

// Get total number of services
$totalServices = $database->fetchAll("SELECT COUNT(*) as count FROM services WHERE is_active = 1")[0]['count'];
$totalPages = ceil($totalServices / $servicesPerPage);

// Get paginated services with variations and category/subcategory info using robust subquery approach
$services = $database->fetchAll(
    "SELECT s.*,
            sc.name as category_name,
            ss.name as subcategory_name,
            COALESCE(v.variation_count, 0) as variation_count,
            v.variations_data
     FROM services s
     LEFT JOIN service_categories sc ON s.category_id = sc.id
     LEFT JOIN service_subcategories ss ON s.subcategory_id = ss.id
     LEFT JOIN (
         SELECT service_id,
                COUNT(*) as variation_count,
                GROUP_CONCAT(
                    CONCAT(id, ':', name, ':', price, ':', duration, ':', is_active)
                    ORDER BY sort_order ASC, name ASC
                    SEPARATOR '|'
                ) as variations_data
         FROM service_variations
         WHERE is_active = 1
         GROUP BY service_id
     ) v ON s.id = v.service_id
     WHERE s.is_active = 1
     ORDER BY s.name ASC
     LIMIT ? OFFSET ?",
    [$servicesPerPage, $offset]
);

// Parse variations data for each service and ensure no duplicates
$uniqueServices = [];
$seenServiceIds = [];

foreach ($services as $service) {
    // Skip if we've already seen this service ID (extra safety measure)
    if (isset($seenServiceIds[$service['id']])) {
        continue;
    }
    $seenServiceIds[$service['id']] = true;

    $service['variations'] = [];
    if ($service['variations_data']) {
        $variationsData = explode('|', $service['variations_data']);
        foreach ($variationsData as $varData) {
            $parts = explode(':', $varData);
            if (count($parts) >= 5) {
                $service['variations'][] = [
                    'id' => $parts[0],
                    'name' => $parts[1],
                    'price' => floatval($parts[2]),
                    'duration' => intval($parts[3]),
                    'is_active' => (bool)$parts[4]
                ];
            }
        }
    }
    unset($service['variations_data']);

    $uniqueServices[] = $service;
}

// Replace the original services array with the deduplicated one
$services = $uniqueServices;

$packages = $database->fetchAll("SELECT * FROM packages WHERE is_active = 1 ORDER BY name ASC");
$staff = $database->fetchAll("
    SELECT
        u.id,
        u.name,
        u.email,
        u.phone,
        u.role,
        u.is_active,
        u.created_at,
        u.updated_at,
        GROUP_CONCAT(
            CONCAT(s.name, ' (', ss.proficiency_level, ')')
            SEPARATOR '|'
        ) as specialties
    FROM users u
    LEFT JOIN staff_specialties ss ON u.id = ss.user_id
    LEFT JOIN services s ON ss.service_id = s.id
    WHERE u.role = 'STAFF'
    GROUP BY u.id, u.name, u.email, u.phone, u.role, u.is_active, u.created_at, u.updated_at
    ORDER BY u.name ASC
");

// Get package services for each package
foreach ($packages as $index => $package) {
    // Get all services (both catalog and manual) for this package
    $packages[$index]['services'] = getPackageServices($package['id']);

    // Calculate total duration - use package_duration if set, otherwise calculate from services
    if ($package['package_duration'] > 0) {
        $packages[$index]['total_duration'] = $package['package_duration'];
    } else {
        $packages[$index]['total_duration'] = array_sum(array_column($packages[$index]['services'], 'duration'));
    }

    // Calculate savings from services with prices
    $originalPrice = 0;
    foreach ($packages[$index]['services'] as $service) {
        if ($service['price'] > 0) {
            $originalPrice += $service['price'];
        }
    }
    $packages[$index]['savings'] = max(0, $originalPrice - $package['price']);
}

$pageTitle = "Book Appointment";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<style>
/* Minimalist Professional Booking Page Styles */
.booking-container {
    background: #ffffff;
    min-height: 100vh;
}

.booking-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.booking-step {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 640px) {
    .booking-step {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

.step-number {
    width: 2rem;
    height: 2rem;
    border-radius: 4px;
    background: #49a75c;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    margin-right: 0.75rem;
}

.service-option, .package-option {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
}

.service-option:hover, .package-option:hover {
    border-color: #49a75c;
}

.service-option.selected, .package-option.selected {
    background: #f0f9ff;
    border-color: #49a75c;
    border-width: 2px;
    box-shadow: 0 0 0 1px #49a75c;
}

/* Regular Staff Cards */
.staff-option {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: visible;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Best Match Staff Cards - More Prominent Styling */
.staff-option.best-match {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.08), rgba(37, 99, 235, 0.08));
    border: 3px solid #49a75c;
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.2);
    transform: scale(1.02);
}

.staff-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    border-radius: 0 0 0 14px;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.staff-option.best-match::before {
    transform: scaleY(1);
    width: 6px;
    background: linear-gradient(135deg, #49a75c, #059669);
}

.staff-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    border-color: #49a75c;
}

.staff-option.best-match:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(73, 167, 92, 0.25);
}

.staff-option:hover::before {
    transform: scaleY(1);
}

.staff-option.selected {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    border-color: #49a75c;
    border-width: 3px;
    box-shadow: 0 15px 35px rgba(73, 167, 92, 0.2);
    transform: translateY(-2px);
}

.staff-option.selected.best-match {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.15), rgba(37, 99, 235, 0.15));
    border-color: #059669;
    box-shadow: 0 20px 45px rgba(73, 167, 92, 0.3);
    transform: translateY(-2px) scale(1.02);
}

.staff-option.selected::before {
    transform: scaleY(1);
}

/* Staff Card Content Styling */
.staff-option h4 {
    color: #1f2937 !important;
    font-weight: 700 !important;
    line-height: 1.2;
    word-wrap: break-word;
    hyphens: auto;
}

.staff-option.best-match h4 {
    color: #065f46 !important;
}

.staff-option p {
    color: #4b5563 !important;
    font-weight: 600 !important;
}

.staff-option.best-match p {
    color: #047857 !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .staff-option {
        min-height: 200px;
        padding: 1.25rem;
    }

    .staff-option h4 {
        font-size: 1.125rem;
    }

    .staff-option p {
        font-size: 0.875rem;
    }
}

.form-input {
    width: 100%;
    padding: 1rem;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    color: #374151;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus {
    outline: none;
    border-color: #49a75c;
    box-shadow: 0 0 0 4px rgba(73, 167, 92, 0.1);
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #059669);
    color: white;
    border: none;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    color: #374151;
    border: 2px solid #e5e7eb;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-color: #49a75c;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Better responsive spacing */
@media (max-width: 640px) {
    .form-input {
        padding: 0.625rem;
        font-size: 0.875rem;
    }

    .btn-primary, .btn-secondary {
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }

    .service-option, .package-option, .staff-option {
        padding: 0.75rem;
    }
}

.notification {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.notification.success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.notification.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

/* Service Description HTML Styling */
.service-description h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.service-description h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.service-description p {
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.service-description ul {
    margin-bottom: 0.75rem;
    padding-left: 1rem;
}

.service-description li {
    margin-bottom: 0.25rem;
    list-style: none;
}

.service-description strong {
    font-weight: 600;
    color: #374151;
}

/* Responsive Design - Full Width Usage */
.booking-container {
    padding: 1rem;
}

@media (min-width: 640px) {
    .booking-container {
        padding: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .booking-container {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .booking-container {
        padding: 0.75rem;
    }

    .booking-step {
        padding: 1rem;
    }

    .step-number {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }

    .booking-card {
        margin-bottom: 1rem;
    }
}
</style>

<!-- Notification -->
<div id="notification" class="fixed top-4 right-4 z-50 transform transition-all duration-300 translate-x-full opacity-0 flex items-center p-3 rounded-lg border bg-white shadow-lg" style="min-width: auto; max-width: calc(100vw - 2rem); width: fit-content;">
    <div class="flex-shrink-0 text-redolence-green mr-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
        </svg>
    </div>
    <div class="flex-1 mr-2">
        <div id="notificationMessage" class="text-gray-900 text-sm font-medium"></div>
    </div>
    <button onclick="hideNotification()" class="flex-shrink-0 text-gray-400 hover:text-gray-600 p-1">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
    </button>
</div>

<div class="booking-container">
    <div class="w-full px-4 py-6">

        <!-- Page Header -->
        <div class="booking-card p-4 sm:p-6 mb-4 sm:mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
                        Book Appointment
                    </h1>
                    <p class="text-gray-600 text-sm sm:text-base">
                        Choose your service, preferred staff, and schedule your visit
                    </p>
                </div>

                <div class="flex gap-2 sm:gap-3">
                    <a href="<?= getBasePath() ?>/customer" class="btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                        <span class="hidden sm:inline">Back</span>
                    </a>
                    <a href="<?= getBasePath() ?>/customer/bookings" class="btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                        Bookings
                    </a>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="notification <?= $messageType ?>">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <?php if ($messageType === 'success'): ?>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    <?php else: ?>
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    <?php endif; ?>
                </svg>
                <div>
                    <?= htmlspecialchars($message) ?>
                    <?php if ($messageType === 'success'): ?>
                        <div class="mt-2">
                            <a href="<?= getBasePath() ?>/customer/bookings" class="text-redolence-green hover:text-green-700 underline font-medium transition-colors duration-200">View your bookings</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Booking Form -->
        <div class="booking-card">
            <div class="p-8">
                <form id="bookingForm" method="POST" class="space-y-8">
                    <input type="hidden" name="action" value="create_booking">

                    <!-- Step 1: Service or Package Selection -->
                    <div class="booking-step" id="step1">
                        <div class="flex items-center mb-6">
                            <div class="step-number">1</div>
                            <h3 class="text-2xl font-bold text-gray-900">
                                Choose Service or Package
                            </h3>
                        </div>

                        <!-- Search and Filter Section -->
                        <div class="mb-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                <!-- Search Bar -->
                                <div>
                                    <div class="relative">
                                        <input type="text"
                                               id="searchInput"
                                               placeholder="Search services..."
                                               class="form-input pr-8">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <!-- Category Filter -->
                                <div>
                                    <select id="categoryFilter" class="form-input">
                                        <option value="">All Categories</option>
                                        <?php foreach ($allCategories as $category): ?>
                                            <option value="<?= htmlspecialchars($category['name']) ?>"><?= htmlspecialchars($category['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <!-- Subcategory Filter -->
                                <div>
                                    <select id="subcategoryFilter" class="form-input" disabled>
                                        <option value="">All Subcategories</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Active Filters -->
                            <div id="activeFilters" class="mt-3 flex flex-wrap gap-2"></div>
                        </div>

                        <!-- Selection Type Tabs -->
                        <div class="flex mb-6 bg-gray-100 rounded-lg p-1">
                            <button type="button" id="servicesTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium bg-white text-gray-900 shadow-sm">
                                Services
                            </button>
                            <button type="button" id="packagesTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900">
                                Packages
                            </button>
                        </div>

                        <!-- Individual Services -->
                        <div id="servicesSection" class="selection-section">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                <?php foreach ($services as $service): ?>
                                    <div class="service-option"
                                         data-service-id="<?= $service['id'] ?>"
                                         data-service-price="<?= $service['price'] ?>"
                                         data-service-duration="<?= $service['duration'] ?>"
                                         data-has-variations="<?= !empty($service['variations']) ? 'true' : 'false' ?>"
                                         data-type="service">
                                        <div class="flex items-center justify-between mb-4">
                                            <h4 class="font-bold text-gray-900 text-lg"><?= html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8') ?></h4>
                                            <?php if (shouldShowPricing()): ?>
                                                <?php if (!empty($service['variations'])): ?>
                                                    <span class="text-redolence-green font-bold text-lg">Multiple Options</span>
                                                <?php else: ?>
                                                    <span class="text-redolence-green font-bold text-lg"><?= $safeCurrencySymbol ?> <?= number_format($service['price'], 2) ?></span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-redolence-green font-bold text-lg">Contact for pricing</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-gray-600 mb-4 leading-relaxed service-description"><?= html_entity_decode($service['description'], ENT_QUOTES, 'UTF-8') ?></div>

                                        <?php if (!empty($service['variations'])): ?>
                                            <div class="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                                <p class="text-sm text-blue-700 font-medium mb-2">Available Options:</p>
                                                <div class="space-y-1">
                                                    <?php foreach ($service['variations'] as $variation): ?>
                                                        <div class="flex justify-between text-sm text-gray-700">
                                                            <span><?= html_entity_decode($variation['name'], ENT_QUOTES, 'UTF-8') ?></span>
                                                            <?php if (shouldShowPricing()): ?>
                                                                <span class="text-blue-600 font-medium"><?= $safeCurrencySymbol ?> <?= number_format($variation['price'], 2) ?></span>
                                                            <?php else: ?>
                                                                <span class="text-blue-600 font-medium">Contact for pricing</span>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-200">
                                            <?php if (!empty($service['variations'])): ?>
                                                <span>⏱️ Various durations</span>
                                            <?php else: ?>
                                                <span>⏱️ <?= $service['duration'] ?> minutes</span>
                                            <?php endif; ?>
                                            <span>🏷️ <?= html_entity_decode($service['category_name'] ?? $service['category'], ENT_QUOTES, 'UTF-8') ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Pagination Controls -->
                            <?php if ($totalPages > 1): ?>
                                <div class="mt-8 flex justify-center items-center space-x-2">
                                    <?php if ($currentPage > 1): ?>
                                        <a href="?page=<?= $currentPage - 1 ?>" class="btn-secondary px-3 py-2" data-page="<?= $currentPage - 1 ?>">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                            </svg>
                                        </a>
                                    <?php endif; ?>

                                    <?php
                                    // Show page numbers with ellipsis
                                    $startPage = max(1, $currentPage - 2);
                                    $endPage = min($totalPages, $currentPage + 2);

                                    if ($startPage > 1) {
                                        echo '<a href="?page=1" class="btn-secondary px-3 py-2" data-page="1">1</a>';
                                        if ($startPage > 2) {
                                            echo '<span class="px-2 text-gray-500">...</span>';
                                        }
                                    }

                                    for ($i = $startPage; $i <= $endPage; $i++) {
                                        $activeClass = $i === $currentPage ? 'btn-primary px-3 py-2' : 'btn-secondary px-3 py-2';
                                        echo "<a href='?page=$i' class='$activeClass' data-page='$i'>$i</a>";
                                    }

                                    if ($endPage < $totalPages) {
                                        if ($endPage < $totalPages - 1) {
                                            echo '<span class="px-2 text-gray-500">...</span>';
                                        }
                                        echo "<a href='?page=$totalPages' class='btn-secondary px-3 py-2' data-page='$totalPages'>$totalPages</a>";
                                    }
                                    ?>

                                    <?php if ($currentPage < $totalPages): ?>
                                        <a href="?page=<?= $currentPage + 1 ?>" class="btn-secondary px-3 py-2" data-page="<?= $currentPage + 1 ?>">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Service Packages -->
                        <div id="packagesSection" class="selection-section hidden">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($packages as $package): ?>
                                    <div class="package-option"
                                         data-package-id="<?= $package['id'] ?>"
                                         data-package-price="<?= $package['price'] ?>"
                                         data-package-duration="<?= $package['total_duration'] ?>"
                                         data-type="package">
                                        <div class="flex items-center justify-between mb-4">
                                            <h4 class="font-bold text-gray-900 text-lg"><?= html_entity_decode($package['name'], ENT_QUOTES, 'UTF-8') ?></h4>
                                            <div class="text-right">
                                                <?php if (shouldShowPricing()): ?>
                                                    <span class="text-redolence-green font-bold text-lg"><?= $safeCurrencySymbol ?> <?= number_format($package['price'], 2) ?></span>
                                                    <?php if ($package['savings'] > 0): ?>
                                                        <div class="text-sm text-green-600 font-semibold">Save <?= $safeCurrencySymbol ?> <?= number_format($package['savings'], 2) ?></div>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-redolence-green font-bold text-lg">Contact for pricing</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <?php if (!empty($package['description'])): ?>
                                            <div class="text-gray-600 mb-4 service-description"><?= html_entity_decode($package['description'], ENT_QUOTES, 'UTF-8') ?></div>
                                        <?php endif; ?>

                                        <div class="mb-3">
                                            <p class="text-sm text-gray-700 font-medium mb-2">Includes:</p>
                                            <div class="flex flex-wrap gap-1">
                                                <?php foreach ($package['services'] as $service): ?>
                                                    <span class="inline-block px-2 py-1 bg-gray-100 text-xs rounded text-gray-700 border">
                                                        <?= html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8') ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-200">
                                            <span>⏱️ <?= $package['total_duration'] ?> minutes total</span>
                                            <span>📦 <?= count($package['services']) ?> services</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <input type="hidden" id="service_id" name="service_id">
                        <input type="hidden" id="service_variation_id" name="service_variation_id">
                        <input type="hidden" id="package_id" name="package_id">
                    </div>

                    <!-- Step 2: Staff Selection -->
                    <div class="booking-step hidden" id="step2">
                        <div class="flex items-center mb-6">
                            <div class="step-number">2</div>
                            <h3 class="text-2xl font-bold text-gray-900">
                                Choose Staff Member
                            </h3>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($staff as $member): ?>
                                <div class="staff-option <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'best-match' : '' ?>"
                                     data-staff-id="<?= $member['id'] ?>">

                                    <!-- Best Match Badge - More Prominent -->
                                    <?php if (isset($member['is_best_fit']) && $member['is_best_fit']): ?>
                                        <div class="absolute -top-3 -right-3 z-20">
                                            <div class="relative">
                                                <div class="w-14 h-14 bg-gradient-to-r from-yellow-400 via-yellow-500 to-orange-500 rounded-full flex items-center justify-center shadow-xl border-4 border-white">
                                                    <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                    </svg>
                                                </div>
                                                <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-ping opacity-40"></div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Staff Avatar and Info -->
                                    <div class="flex flex-col items-center text-center mb-4">
                                        <!-- Avatar -->
                                        <div class="flex-shrink-0 mb-3">
                                            <div class="w-20 h-20 rounded-full <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'bg-gradient-to-br from-green-100 via-blue-100 to-purple-100 ring-4 ring-green-200' : 'bg-gradient-to-br from-gray-100 to-gray-200 ring-2 ring-gray-200' ?> flex items-center justify-center shadow-lg">
                                                <svg class="w-10 h-10 <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'text-green-600' : 'text-gray-500' ?>" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                        </div>

                                        <!-- Name and Role -->
                                        <div class="w-full">
                                            <h4 class="font-bold text-gray-900 text-xl mb-1 leading-tight">
                                                <?= html_entity_decode($member['name'], ENT_QUOTES, 'UTF-8') ?>
                                            </h4>

                                            <!-- Best Match Badge - Inline -->
                                            <?php if (isset($member['is_best_fit']) && $member['is_best_fit']): ?>
                                                <div class="mb-2">
                                                    <span class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-yellow-100 via-yellow-200 to-orange-100 text-yellow-800 text-sm rounded-full font-bold border-2 border-yellow-300 shadow-sm">
                                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                        </svg>
                                                        BEST MATCH
                                                    </span>
                                                </div>
                                            <?php endif; ?>

                                            <p class="text-base text-gray-700 font-semibold mb-3">
                                                <?= html_entity_decode($member['role'], ENT_QUOTES, 'UTF-8') ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Specialties -->
                                    <?php
                                    $specialtiesList = !empty($member['specialties']) ? explode('|', $member['specialties']) : [];
                                    if (!empty($specialtiesList)):
                                    ?>
                                        <div class="mt-auto">
                                            <div class="flex flex-wrap gap-2 justify-center">
                                                <?php foreach (array_slice($specialtiesList, 0, 3) as $specialty): ?>
                                                    <span class="inline-block px-3 py-1 <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'bg-gradient-to-r from-green-50 to-blue-50 text-green-700 border border-green-200' : 'bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border border-gray-200' ?> text-xs rounded-full font-medium">
                                                        <?= html_entity_decode(trim($specialty), ENT_QUOTES, 'UTF-8') ?>
                                                    </span>
                                                <?php endforeach; ?>
                                                <?php if (count($specialtiesList) > 3): ?>
                                                    <span class="inline-block px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 text-xs rounded-full border border-blue-200 font-medium">
                                                        +<?= count($specialtiesList) - 3 ?> more
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <input type="hidden" id="staff_id" name="staff_id" required>
                    </div>

                    <!-- Step 3: Date & Time Selection -->
                    <div class="booking-step hidden" id="step3">
                        <div class="flex items-center mb-8">
                            <div class="step-number">3</div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-2">
                                    Choose Date & Time
                                </h3>
                                <p class="text-gray-600">Select your preferred appointment date and time</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Date Selection Card -->
                            <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-200 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-gradient-to-br from-redolence-green to-blue-500 rounded-xl flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <label for="date" class="block text-lg font-bold text-gray-900">Select Date</label>
                                        <p class="text-sm text-gray-600">Choose your preferred appointment date</p>
                                    </div>
                                </div>

                                <div class="relative">
                                    <input type="date" id="date" name="date" required min="<?= date('Y-m-d') ?>"
                                           class="w-full px-4 py-4 bg-white border-2 border-gray-200 rounded-xl text-lg font-medium text-gray-900 focus:outline-none focus:border-redolence-green focus:ring-4 focus:ring-redolence-green/10 transition-all duration-300">
                                </div>
                            </div>

                            <!-- Time Selection Card -->
                            <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-200 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <label for="start_time" class="block text-lg font-bold text-gray-900">Start Time</label>
                                        <p class="text-sm text-gray-600">Choose your preferred start time</p>
                                    </div>
                                </div>

                                <div class="relative mb-4">
                                    <input type="time" id="start_time" name="start_time" required
                                           class="w-full px-4 py-4 bg-white border-2 border-gray-200 rounded-xl text-lg font-medium text-gray-900 focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-300">
                                    <input type="hidden" id="end_time" name="end_time" required>
                                </div>

                                <!-- Duration Info -->
                                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-100">
                                    <div class="flex items-center text-sm text-blue-700">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                        </svg>
                                        <span class="font-medium">End time will be calculated automatically based on service duration</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Status -->
                        <div id="availabilityStatus" class="mt-8 hidden">
                            <div id="availabilityMessage" class="p-6 rounded-2xl flex items-center bg-gradient-to-r from-blue-50 to-green-50 border-2 border-blue-200 shadow-sm"></div>
                        </div>
                    </div>

                    <!-- Step 4: Additional Options -->
                    <div class="booking-step hidden" id="step4">
                        <div class="flex items-center mb-8">
                            <div class="step-number">4</div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-2">
                                    Additional Options
                                </h3>
                                <p class="text-gray-600">Customize your appointment experience</p>
                            </div>
                        </div>

                        <div class="space-y-8">
                            <!-- Points Usage -->
                            <?php if (shouldShowPricing()): ?>
                                <div class="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-blue-200 rounded-2xl p-6 shadow-sm">
                                    <div class="flex items-center mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="text-xl font-bold text-gray-900 mb-1">Use Loyalty Points</h4>
                                            <p class="text-sm text-gray-600">You have <span class="font-bold text-blue-600"><?= number_format($profile['points']) ?></span> points available (1 point = <?= $safeCurrencySymbol ?> 10)</p>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="points_used" class="block text-sm font-semibold text-gray-700 mb-2">Points to Use</label>
                                            <input type="number" id="points_used" name="points_used" min="0" max="<?= $profile['points'] ?>" value="0"
                                                   class="form-input" placeholder="Enter points to use">
                                        </div>
                                        <div class="flex items-end">
                                            <div class="bg-white rounded-xl p-4 border border-blue-200 w-full">
                                                <div class="text-sm text-gray-600 mb-1">Your Discount</div>
                                                <div class="text-2xl font-bold text-green-600">
                                                    <?= $safeCurrencySymbol ?> <span id="pointsDiscount">0.00</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Special Notes -->
                            <div class="bg-gradient-to-br from-gray-50 to-white border-2 border-gray-200 rounded-2xl p-6 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <label for="notes" class="block text-xl font-bold text-gray-900 mb-1">Special Requests</label>
                                        <p class="text-sm text-gray-600">Let us know if you have any special requirements</p>
                                    </div>
                                </div>

                                <textarea id="notes" name="notes" rows="4"
                                          class="form-input resize-none"
                                          placeholder="Any special requests, allergies, preferences, or notes for your appointment..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Summary -->
                    <div class="booking-step hidden" id="summary">
                        <div class="flex items-center mb-8">
                            <div class="step-number">5</div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-2">
                                    Booking Summary
                                </h3>
                                <p class="text-gray-600">Review your appointment details before confirming</p>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl border-2 border-gray-200 shadow-lg overflow-hidden">
                            <!-- Header -->
                            <div class="bg-gradient-to-r from-redolence-green to-blue-500 px-6 py-4">
                                <h4 class="text-white font-bold text-lg flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Appointment Details
                                </h4>
                            </div>

                            <!-- Content -->
                            <div class="p-6 space-y-6">
                                <!-- Service Information -->
                                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center mr-3">
                                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <span class="text-sm text-gray-500 font-medium">Service</span>
                                                <div class="text-gray-900 font-bold text-lg" id="summaryService">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Staff Information -->
                                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-green-100 to-blue-100 rounded-xl flex items-center justify-center mr-3">
                                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <span class="text-sm text-gray-500 font-medium">Staff Member</span>
                                                <div class="text-gray-900 font-bold text-lg" id="summaryStaff">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Date & Time Information -->
                                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl flex items-center justify-center mr-3">
                                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <span class="text-sm text-gray-500 font-medium">Date & Time</span>
                                                <div class="text-gray-900 font-bold text-lg" id="summaryDateTime">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Duration Information -->
                                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center mr-3">
                                                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <span class="text-sm text-gray-500 font-medium">Duration</span>
                                                <div class="text-gray-900 font-bold text-lg" id="summaryDuration">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if (shouldShowPricing()): ?>
                                    <!-- Pricing Section -->
                                    <div class="border-t-2 border-gray-200 pt-6">
                                        <h5 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                            </svg>
                                            Pricing Details
                                        </h5>

                                        <div class="space-y-3">
                                            <div class="flex justify-between items-center py-2">
                                                <span class="text-gray-600 font-medium">Service Price:</span>
                                                <span class="text-gray-900 font-bold text-lg" id="summaryPrice"><?= $safeCurrencySymbol ?> 0.00</span>
                                            </div>

                                            <div class="flex justify-between items-center py-2" id="pointsDiscountRow" style="display: none;">
                                                <span class="text-gray-600 font-medium">Points Discount:</span>
                                                <span class="text-green-600 font-bold text-lg" id="summaryPointsDiscount">-<?= $safeCurrencySymbol ?> 0.00</span>
                                            </div>

                                            <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 border-2 border-green-200">
                                                <div class="flex justify-between items-center">
                                                    <span class="text-gray-900 font-bold text-xl">Total Amount:</span>
                                                    <span class="text-green-600 font-bold text-2xl" id="summaryTotal"><?= $safeCurrencySymbol ?> 0.00</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex items-center justify-between pt-8 border-t-2 border-gray-200 mt-8">
                        <button type="button" id="prevBtn" onclick="changeStep(-1)"
                                class="btn-secondary px-6 py-3 text-lg font-semibold" style="display: none;">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            Previous Step
                        </button>

                        <div class="flex-1"></div>

                        <button type="button" id="nextBtn" onclick="changeStep(1)"
                                class="btn-primary px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                            Continue
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>

                        <button type="submit" id="submitBtn"
                                class="btn-primary px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-redolence-green to-blue-500 hover:from-green-600 hover:to-blue-600" style="display: none;">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Confirm Booking
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Service Variation Selection Modal -->
<div id="variationSelectionModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white border border-gray-200 rounded-lg p-6 w-full max-w-lg mx-4 max-h-screen overflow-y-auto shadow-lg">
        <div class="flex justify-between items-center mb-4">
            <h2 id="variationSelectionTitle" class="text-lg font-semibold text-gray-900">Choose Service Option</h2>
            <button onclick="closeVariationSelectionModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div id="variationSelectionContent" class="space-y-3">
            <!-- Variations will be loaded here -->
        </div>
    </div>
</div>

    <script>
        let currentStep = 1;
        let selectedService = null;
        let selectedPackage = null;
        let selectedStaff = null;
        let selectionType = 'service'; // 'service' or 'package'

        // Tab switching
        document.getElementById('servicesTab').addEventListener('click', function() {
            switchTab('services');
        });

        document.getElementById('packagesTab').addEventListener('click', function() {
            switchTab('packages');
        });

        function switchTab(type) {
            // Update tab appearance
            const servicesTab = document.getElementById('servicesTab');
            const packagesTab = document.getElementById('packagesTab');
            const servicesSection = document.getElementById('servicesSection');
            const packagesSection = document.getElementById('packagesSection');

            if (type === 'services') {
                servicesTab.classList.add('bg-salon-gold', 'text-black');
                servicesTab.classList.remove('text-gray-300');
                packagesTab.classList.remove('bg-salon-gold', 'text-black');
                packagesTab.classList.add('text-gray-300');
                servicesSection.classList.remove('hidden');
                packagesSection.classList.add('hidden');
                selectionType = 'service';
            } else {
                packagesTab.classList.add('bg-salon-gold', 'text-black');
                packagesTab.classList.remove('text-gray-300');
                servicesTab.classList.remove('bg-salon-gold', 'text-black');
                servicesTab.classList.add('text-gray-300');
                packagesSection.classList.remove('hidden');
                servicesSection.classList.add('hidden');
                selectionType = 'package';
            }

            // Clear previous selections
            clearSelections();
        }

        function clearSelections() {
            selectedService = null;
            selectedPackage = null;
            document.getElementById('service_id').value = '';
            document.getElementById('package_id').value = '';
            document.getElementById('nextBtn').disabled = true;

            // Remove visual selections
            document.querySelectorAll('.service-option, .package-option').forEach(opt => {
                opt.classList.remove('border-salon-gold', 'bg-secondary-700');
            });
        }

        // Service selection is now handled by initializeServiceSelection() function

        // Package selection is now handled by initializePackageSelection() function

        // Staff selection
        document.querySelectorAll('.staff-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.staff-option').forEach(opt => {
                    opt.classList.remove('selected');
                });

                // Add selection to clicked option
                this.classList.add('selected');

                // Store selected staff
                selectedStaff = {
                    id: this.dataset.staffId,
                    name: this.querySelector('h4').textContent.trim()
                };

                document.getElementById('staff_id').value = selectedStaff.id;
                document.getElementById('nextBtn').disabled = false;
            });
        });

        // Date and time selection with validation
        document.getElementById('date').addEventListener('change', function() {
            validateAvailability();
        });

        document.getElementById('start_time').addEventListener('change', function() {
            calculateEndTime();
            validateAvailability();
        });

        // Points usage calculation
        <?php if (shouldShowPricing()): ?>
            document.getElementById('points_used').addEventListener('input', function() {
                const pointsUsed = parseInt(this.value) || 0;
                const discount = pointsUsed * 10; // 1 point = TSH 10
                document.getElementById('pointsDiscount').textContent = discount;
                updateSummary();
            });
        <?php endif; ?>

        function changeStep(direction) {
            const totalSteps = 5;
            const newStep = currentStep + direction;

            if (newStep < 1 || newStep > totalSteps) return;

            // Validate current step before proceeding
            if (direction > 0 && !validateStep(currentStep)) return;

            // Hide current step
            document.getElementById(`step${currentStep}`).classList.add('hidden');
            if (currentStep === totalSteps) {
                document.getElementById('summary').classList.add('hidden');
            }

            // Show new step
            currentStep = newStep;
            
            // If moving to staff selection step (step 2), fetch suggested staff
            if (currentStep === 2) {
                fetchSuggestedStaff();
            }
            
            if (currentStep === totalSteps) {
                document.getElementById('summary').classList.remove('hidden');
                updateSummary();
            } else {
                document.getElementById(`step${currentStep}`).classList.remove('hidden');
            }

            // Update navigation buttons
            updateNavigation();
        }

        function validateStep(step) {
            switch(step) {
                case 1:
                    if (!selectedService && !selectedPackage) {
                        showNotification('Please select a service or package to continue');
                        return false;
                    }
                    break;
                case 2:
                    if (!selectedStaff) {
                        showNotification('Please select a staff member');
                        return false;
                    }
                    break;
                case 3:
                    const date = document.getElementById('date').value;
                    const startTime = document.getElementById('start_time').value;
                    if (!date || !startTime) {
                        showNotification('Please select both a date and time');
                        return false;
                    }

                    const availabilityStatus = document.getElementById('availabilityStatus');
                    if (availabilityStatus.classList.contains('error')) {
                        showNotification('Selected staff is not available at this time. Please choose a different time or date.');
                        return false;
                    }
                    break;
            }
            return true;
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');

            // Show/hide previous button
            prevBtn.style.display = currentStep > 1 ? 'block' : 'none';

            // Show/hide next/submit buttons
            if (currentStep === 5) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            } else {
                nextBtn.style.display = 'block';
                submitBtn.style.display = 'none';
            }
        }

        function calculateEndTime() {
            const startTime = document.getElementById('start_time').value;
            if (!startTime) return;

            const currentSelection = selectedService || selectedPackage;
            if (!currentSelection) return;

            // Calculate end time based on service/package duration
            const duration = currentSelection.duration; // in minutes
            const startDateTime = new Date(`2000-01-01T${startTime}:00`);
            const endDateTime = new Date(startDateTime.getTime() + duration * 60000);

            const endTime = endDateTime.toTimeString().slice(0, 5);
            document.getElementById('end_time').value = endTime;
        }

        async function validateAvailability() {
            const date = document.getElementById('date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            if (!date || !startTime || !selectedStaff) {
                hideAvailabilityStatus();
                return;
            }

            const currentSelection = selectedService || selectedPackage;
            if (!currentSelection) return;

            try {
                const requestBody = {
                    date: date,
                    start_time: startTime,
                    end_time: endTime,
                    staff_id: selectedStaff.id
                };

                if (selectedService) {
                    requestBody.service_id = selectedService.id;
                } else {
                    requestBody.package_id = selectedPackage.id;
                }

                const response = await fetch('<?= getBasePath() ?>/api/customer/validate-availability.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                showAvailabilityStatus(data);

            } catch (error) {
                console.error('Error validating availability:', error);
                showAvailabilityStatus({
                    success: false,
                    message: 'Error checking availability. Please try again.'
                });
            }
        }

        function showAvailabilityStatus(data) {
            const statusDiv = document.getElementById('availabilityStatus');
            const messageDiv = document.getElementById('availabilityMessage');

            statusDiv.classList.remove('hidden');

            if (data.success) {
                statusDiv.classList.remove('error');
                statusDiv.classList.add('success');
                messageDiv.className = 'text-sm p-2 rounded bg-green-100 text-green-700 border border-green-400';
                messageDiv.innerHTML = '<i class="fas fa-check-circle mr-2"></i>' + (data.message || 'Staff is available at this time');
                document.getElementById('nextBtn').disabled = false;
            } else {
                statusDiv.classList.remove('success');
                statusDiv.classList.add('error');
                messageDiv.className = 'text-sm p-2 rounded bg-red-100 text-red-700 border border-red-400';
                messageDiv.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + (data.message || 'Staff is not available at this time');
                document.getElementById('nextBtn').disabled = true;
            }
        }

        function hideAvailabilityStatus() {
            document.getElementById('availabilityStatus').classList.add('hidden');
            document.getElementById('nextBtn').disabled = false;
        }

        function updateSummary() {
            const currentSelection = selectedService || selectedPackage;
            const date = document.getElementById('date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            if (!currentSelection || !selectedStaff || !date || !startTime) return;

            <?php if (shouldShowPricing()): ?>
                let pointsUsed = parseInt(document.getElementById('points_used').value) || 0;
                let pointsDiscount = pointsUsed * 10; // 1 point = TSH 10
                let total = Math.max(0, currentSelection.price - pointsDiscount);
            <?php else: ?>
                let pointsUsed = 0;
                let pointsDiscount = 0;
                let total = 0;
            <?php endif; ?>

            // Format time display
            const startTimeFormatted = new Date(`2000-01-01T${startTime}:00`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const endTimeFormatted = new Date(`2000-01-01T${endTime}:00`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const timeDisplay = `${startTimeFormatted} - ${endTimeFormatted}`;

            // Display service name with variation if applicable
            let serviceName = currentSelection.name;
            if (currentSelection.variationName) {
                serviceName += ` (${currentSelection.variationName})`;
            }

            document.getElementById('summaryService').textContent = serviceName;
            document.getElementById('summaryStaff').textContent = selectedStaff.name;
            document.getElementById('summaryDateTime').textContent =
                new Date(date).toLocaleDateString() + ' at ' + timeDisplay;
            document.getElementById('summaryDuration').textContent = currentSelection.duration + ' minutes';
            <?php if (shouldShowPricing()): ?>
                document.getElementById('summaryPrice').textContent = '<?= $safeCurrencySymbol ?> ' + parseInt(currentSelection.price).toLocaleString();

                if (pointsUsed > 0) {
                    document.getElementById('pointsDiscountRow').style.display = 'flex';
                    document.getElementById('summaryPointsDiscount').textContent = '-<?= $safeCurrencySymbol ?> ' + parseInt(pointsDiscount).toLocaleString();
                } else {
                    document.getElementById('pointsDiscountRow').style.display = 'none';
                }

                document.getElementById('summaryTotal').textContent = '<?= $safeCurrencySymbol ?> ' + parseInt(total).toLocaleString();
            <?php endif; ?>
        }

        // Handle pre-selection with pagination support
        async function handlePreSelection() {
            // Check URL parameters first
            const urlParams = new URLSearchParams(window.location.search);
            const serviceId = urlParams.get('service');
            let variationId = urlParams.get('variation');
            const packageId = urlParams.get('package');

            // Check sessionStorage for pending bookings
            const pendingServiceBooking = sessionStorage.getItem('pendingServiceBooking');
            const pendingPackageBooking = sessionStorage.getItem('pendingPackageBooking');

            let serviceToSelect = null;
            let packageToSelect = null;

            // Handle service pre-selection
            if (serviceId) {
                serviceToSelect = serviceId;
            } else if (pendingServiceBooking) {
                try {
                    const bookingData = JSON.parse(pendingServiceBooking);
                    if (Date.now() - bookingData.timestamp < 3600000) {
                        serviceToSelect = bookingData.serviceId;
                        if (bookingData.variationId) {
                            variationId = bookingData.variationId;
                        }
                    }
                    sessionStorage.removeItem('pendingServiceBooking');
                } catch (e) {
                    console.error('Error parsing pending service booking data:', e);
                    sessionStorage.removeItem('pendingServiceBooking');
                }
            }

            // Handle package pre-selection
            if (packageId) {
                packageToSelect = packageId;
            } else if (pendingPackageBooking) {
                try {
                    const bookingData = JSON.parse(pendingPackageBooking);
                    if (Date.now() - bookingData.timestamp < 3600000) {
                        packageToSelect = bookingData.packageId;
                    }
                    sessionStorage.removeItem('pendingPackageBooking');
                } catch (e) {
                    console.error('Error parsing pending package booking data:', e);
                    sessionStorage.removeItem('pendingPackageBooking');
                }
            }

            // Pre-select service if found
            if (serviceToSelect) {
                await handleServicePreSelection(serviceToSelect, variationId);
            }
            // Pre-select package if found (packages take priority over services if both are present)
            else if (packageToSelect) {
                await handlePackagePreSelection(packageToSelect);
            }
        }

        // Handle service pre-selection with pagination support
        async function handleServicePreSelection(serviceId, variationId = null) {
            // First check if service is on current page
            const serviceOption = document.querySelector(`[data-service-id="${serviceId}"]`);

            if (serviceOption) {
                // Service found on current page - select it
                switchTab('services');
                setTimeout(() => {
                    if (variationId) {
                        // If variation ID is provided, fetch variation data and select with variation
                        fetch(`<?= getBasePath() ?>/api/customer/service_variations.php?service_id=${serviceId}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success && data.variations) {
                                    const variation = data.variations.find(v => v.id === variationId);
                                    if (variation) {
                                        selectService(serviceOption, variationId, variation);
                                    } else {
                                        selectService(serviceOption);
                                    }
                                } else {
                                    selectService(serviceOption);
                                }
                                skipToStaffSelection();
                                showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                            })
                            .catch(error => {
                                console.error('Error fetching variation data:', error);
                                selectService(serviceOption);
                                skipToStaffSelection();
                                showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                            });
                    } else {
                        // No variation, direct selection
                        selectService(serviceOption);
                        skipToStaffSelection();
                        showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                    }
                }, 100);
                return;
            }

            // Service not found on current page - find which page it's on
            try {
                const response = await fetch(`<?= getBasePath() ?>/api/customer/find-service-page.php?service_id=${serviceId}`);
                const data = await response.json();

                if (data.success && data.page) {
                    // Store the service ID for after page load
                    sessionStorage.setItem('pendingServiceSelection', JSON.stringify({
                        serviceId: serviceId,
                        timestamp: Date.now()
                    }));

                    // Navigate to the correct page
                    await navigateToPage(data.page);

                    // After navigation, try selection again
                    setTimeout(() => {
                        const serviceOption = document.querySelector(`[data-service-id="${serviceId}"]`);
                        if (serviceOption) {
                            switchTab('services');
                            setTimeout(() => {
                                serviceOption.click();
                                skipToStaffSelection();
                                showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                            }, 100);
                        }
                        // Clean up
                        sessionStorage.removeItem('pendingServiceSelection');
                    }, 500);
                } else {
                    console.warn('Service not found:', serviceId);
                    showNotification('Selected service not found');
                }
            } catch (error) {
                console.error('Error finding service page:', error);
                showNotification('Error loading selected service');
            }
        }

        // Handle package pre-selection with pagination support
        async function handlePackagePreSelection(packageId) {
            // First check if package is on current page
            const packageOption = document.querySelector(`[data-package-id="${packageId}"]`);

            if (packageOption) {
                // Package found on current page - select it
                switchTab('packages');
                setTimeout(() => {
                    packageOption.click();
                    skipToStaffSelection();
                    showPreSelectionNotification(packageOption.querySelector('h4').textContent.trim(), 'package', true);
                }, 100);
                return;
            }

            // Package not found on current page - packages are not paginated currently
            // But we'll add support for future pagination
            console.warn('Package not found on current page:', packageId);
            showNotification('Selected package not found');
        }

        // Navigate to a specific page
        async function navigateToPage(page) {
            return new Promise((resolve, reject) => {
                const servicesSection = document.getElementById('servicesSection');
                servicesSection.style.opacity = '0.5';

                fetch(`${window.location.pathname}?page=${page}`)
                    .then(response => response.text())
                    .then(html => {
                        const temp = document.createElement('div');
                        temp.innerHTML = html;

                        const newServicesSection = temp.querySelector('#servicesSection');
                        servicesSection.innerHTML = newServicesSection.innerHTML;

                        // Update URL without reload
                        window.history.pushState({}, '', `?page=${page}`);

                        servicesSection.style.opacity = '1';

                        // Reinitialize service selection handlers
                        initializeServiceSelection();

                        resolve();
                    })
                    .catch(error => {
                        console.error('Error navigating to page:', error);
                        servicesSection.style.opacity = '1';
                        reject(error);
                    });
            });
        }

        // Skip to staff selection step when service/package is pre-selected
        function skipToStaffSelection() {
            setTimeout(() => {
                document.getElementById('step1').classList.add('hidden');
                document.getElementById('step2').classList.remove('hidden');
                currentStep = 2;
                updateNavigation();
                fetchSuggestedStaff(); // Fetch staff suggestions when skipping to step 2
            }, 200);
        }

        // Function to fetch suggested staff
        async function fetchSuggestedStaff() {
            try {
                const serviceId = document.getElementById('service_id').value;
                const packageId = document.getElementById('package_id').value;
                
                if (!serviceId && !packageId) {
                    console.error('No service or package selected');
                    return;
                }

                const requestData = serviceId 
                    ? { service_id: serviceId }
                    : { package_id: packageId };

                const response = await fetch('<?= getBasePath() ?>/api/customer/suggest-staff.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                
                if (data.success) {
                    updateStaffSection(data.staff);
                } else {
                    console.error('Error fetching staff:', data.message);
                    showNotification('Error fetching staff suggestions. Please try again.');
                }
            } catch (error) {
                console.error('Error fetching suggested staff:', error);
                showNotification('Error fetching staff suggestions. Please try again.');
            }
        }

        // Function to update staff section with suggested staff
        function updateStaffSection(staffList) {
            const staffContainer = document.querySelector('#step2 .grid');
            
            // Clear previous staff selection
            selectedStaff = null;
            document.getElementById('staff_id').value = '';

            staffContainer.innerHTML = staffList.map(member => `
                <div class="staff-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 hover-lift relative"
                     data-staff-id="${member.id}">
                    ${member.is_best_fit ? `
                        <div class="absolute -top-3 -right-3 w-12 h-12 flex items-center justify-center">
                            <div class="absolute inset-0 bg-salon-gold rounded-full animate-ping opacity-20"></div>
                            <div class="absolute inset-0 bg-salon-gold rounded-full"></div>
                            <i class="fas fa-crown text-black text-lg relative z-10"></i>
                        </div>
                    ` : ''}
                    <div class="flex items-center gap-4 mb-4">
                        <div class="w-16 h-16 rounded-full ${member.is_best_fit ? 'bg-salon-gold' : 'bg-secondary-800'} flex items-center justify-center border-2 ${member.is_best_fit ? 'border-salon-gold shadow-lg shadow-salon-gold/20' : 'border-secondary-700'}">
                            <i class="fas fa-user ${member.is_best_fit ? 'text-black' : 'text-gray-400'} text-xl"></i>
                        </div>
                        <div>
                            <div class="flex items-center gap-2">
                                <h4 class="font-semibold text-white text-lg">${member.name}</h4>
                                ${member.is_best_fit ? `
                                    <span class="px-2 py-1 bg-salon-gold/20 text-salon-gold text-xs rounded-full">
                                        Best Match
                                    </span>
                                ` : ''}
                            </div>
                            <p class="text-sm text-salon-gold font-medium">${member.role}</p>
                        </div>
                    </div>

                    ${member.specialties && member.specialties.length > 0 ? `
                        <div class="flex flex-wrap gap-2 mb-3">
                            ${member.specialties.map(specialty => `
                                <span class="inline-block px-3 py-1 bg-secondary-800/50 border ${member.is_best_fit ? 'border-salon-gold/30' : 'border-secondary-700'} text-xs rounded-full ${member.is_best_fit ? 'text-salon-gold' : 'text-gray-300'}">
                                    ${specialty}
                                </span>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('');

            // Sort the DOM elements to put best fit first
            const staffOptions = Array.from(staffContainer.children);
            staffOptions.sort((a, b) => {
                const aIsBestFit = a.querySelector('.fa-crown') !== null;
                const bIsBestFit = b.querySelector('.fa-crown') !== null;
                return bIsBestFit - aIsBestFit;
            });
            staffContainer.innerHTML = '';
            staffOptions.forEach(option => staffContainer.appendChild(option));

            // Reinitialize staff selection event listeners
            document.querySelectorAll('.staff-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.staff-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });
                    this.classList.add('selected');
                    
                    selectedStaff = {
                        id: this.dataset.staffId,
                        name: this.querySelector('h4').textContent.trim()
                    };

                    document.getElementById('staff_id').value = selectedStaff.id;
                    document.getElementById('nextBtn').disabled = false;
                });
            });
        }

        function showPreSelectionNotification(itemName, itemType = 'service', skipped = false) {
            // Remove any existing notifications first
            const existingNotifications = document.querySelectorAll('.fixed.top-4.right-4');
            existingNotifications.forEach(notification => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });

            // Create a temporary notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-salon-gold text-black px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 max-w-sm';

            const message = skipped
                ? `${itemName} ${itemType} pre-selected - Proceeding to staff selection`
                : `${itemName} ${itemType} pre-selected`;

            notification.innerHTML = `
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-semibold text-sm leading-tight">${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Remove notification after 4 seconds (longer for combined message)
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Initialize
        updateNavigation();

        // Handle pre-selection when page loads
        document.addEventListener('DOMContentLoaded', function() {
            handlePreSelection();
            // Also check for any pending selections from pagination navigation
            setTimeout(checkPendingSelections, 100);
        });

        // If DOM is already loaded, run immediately
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                handlePreSelection();
                setTimeout(checkPendingSelections, 100);
            });
        } else {
            handlePreSelection();
            setTimeout(checkPendingSelections, 100);
        }

        // Add this to your existing JavaScript
        // Initialize pagination handlers (consolidated)
        function initializePaginationHandlers() {
            document.querySelectorAll('.pagination-btn').forEach(btn => {
                // Remove existing listeners to prevent duplicates
                btn.replaceWith(btn.cloneNode(true));
            });

            // Re-select and add listeners
            document.querySelectorAll('.pagination-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = this.dataset.page;

                    // Show loading state
                    const servicesSection = document.getElementById('servicesSection');
                    servicesSection.style.opacity = '0.5';

                    // Fetch new page content
                    fetch(`${window.location.pathname}?page=${page}`)
                        .then(response => response.text())
                        .then(html => {
                            // Create a temporary container
                            const temp = document.createElement('div');
                            temp.innerHTML = html;

                            // Replace services section content
                            const newServicesSection = temp.querySelector('#servicesSection');
                            servicesSection.innerHTML = newServicesSection.innerHTML;

                            // Update URL without reload
                            window.history.pushState({}, '', `?page=${page}`);

                            // Restore opacity
                            servicesSection.style.opacity = '1';

                            // Reinitialize service selection handlers
                            initializeServiceSelection();
                            initializePackageSelection();
                            initializePaginationHandlers();

                            // Check for pending selections after page load
                            checkPendingSelections();
                        })
                        .catch(error => {
                            console.error('Error fetching page:', error);
                            servicesSection.style.opacity = '1';
                        });
                });
            });
        }

        // Check for pending selections (used after pagination)
        function checkPendingSelections() {
            // Check for pending service selection
            const pendingServiceSelection = sessionStorage.getItem('pendingServiceSelection');
            if (pendingServiceSelection) {
                try {
                    const selectionData = JSON.parse(pendingServiceSelection);
                    if (Date.now() - selectionData.timestamp < 10000) { // 10 second timeout
                        const serviceOption = document.querySelector(`[data-service-id="${selectionData.serviceId}"]`);
                        if (serviceOption) {
                            switchTab('services');
                            setTimeout(() => {
                                serviceOption.click();
                                skipToStaffSelection();
                                showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                            }, 100);
                            sessionStorage.removeItem('pendingServiceSelection');
                        }
                    } else {
                        // Timeout - clean up
                        sessionStorage.removeItem('pendingServiceSelection');
                    }
                } catch (e) {
                    console.error('Error parsing pending service selection:', e);
                    sessionStorage.removeItem('pendingServiceSelection');
                }
            }
        }

        // Function to initialize service selection
        function initializeServiceSelection() {
            document.querySelectorAll('.service-option').forEach(option => {
                // Remove existing listeners to prevent duplicates
                option.replaceWith(option.cloneNode(true));
            });

            // Re-select the elements after cloning
            document.querySelectorAll('.service-option').forEach(option => {
                option.addEventListener('click', function() {
                    const hasVariations = this.dataset.hasVariations === 'true';

                    if (hasVariations) {
                        // Show variation selection modal
                        showServiceVariationModal(this.dataset.serviceId, this.querySelector('h4').textContent.trim());
                    } else {
                        // Direct service selection
                        selectService(this);
                    }
                });
            });
        }

        // Function to select a service directly (without variations)
        function selectService(serviceElement, variationId = null, variationData = null) {
            // Clear all selections
            document.querySelectorAll('.service-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            document.querySelectorAll('.package-option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // Select this service
            serviceElement.classList.add('selected');

            // Set form values
            selectedService = {
                id: serviceElement.dataset.serviceId,
                name: serviceElement.querySelector('h4').textContent.trim(),
                price: variationData ? variationData.price : parseFloat(serviceElement.dataset.servicePrice),
                duration: variationData ? variationData.duration : parseInt(serviceElement.dataset.serviceDuration),
                type: 'service',
                variationId: variationId,
                variationName: variationData ? variationData.name : null
            };

            selectedPackage = null;
            document.getElementById('service_id').value = selectedService.id;
            document.getElementById('service_variation_id').value = variationId || '';
            document.getElementById('package_id').value = '';
            document.getElementById('nextBtn').disabled = false;
        }

        // Function to initialize package selection
        function initializePackageSelection() {
            document.querySelectorAll('.package-option').forEach(option => {
                // Remove existing listeners to prevent duplicates
                option.replaceWith(option.cloneNode(true));
            });

            // Re-select the elements after cloning
            document.querySelectorAll('.package-option').forEach(option => {
                option.addEventListener('click', function() {
                    // Clear all selections
                    document.querySelectorAll('.service-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });
                    document.querySelectorAll('.package-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // Select this package
                    this.classList.add('selected');

                    // Set form values
                    selectedPackage = {
                        id: this.dataset.packageId,
                        name: this.querySelector('h4').textContent.trim(),
                        price: parseFloat(this.dataset.packagePrice),
                        duration: parseInt(this.dataset.packageDuration),
                        type: 'package'
                    };

                    selectedService = null;
                    document.getElementById('package_id').value = selectedPackage.id;
                    document.getElementById('service_id').value = '';
                    document.getElementById('service_variation_id').value = '';
                    document.getElementById('nextBtn').disabled = false;
                });
            });
        }

        // Service variation modal functions
        function showServiceVariationModal(serviceId, serviceName) {
            console.log('Showing variation modal for service:', serviceId, serviceName);
            document.getElementById('variationSelectionTitle').textContent = `Choose ${serviceName} Option`;

            // Show loading state
            const content = document.getElementById('variationSelectionContent');
            content.innerHTML = '<div class="text-center py-8"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-salon-gold mx-auto"></div><p class="text-gray-300 mt-4">Loading options...</p></div>';
            document.getElementById('variationSelectionModal').classList.remove('hidden');

            // Fetch service variations
            fetch(`<?= getBasePath() ?>/api/customer/service_variations.php?service_id=${serviceId}`)
                .then(response => {
                    console.log('Variation API response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Variation API data:', data);
                    if (data.success && data.variations && data.variations.length > 0) {
                        displayVariationOptions(data.service, data.variations);
                    } else {
                        console.log('No variations found, selecting service directly');
                        closeVariationSelectionModal();
                        // No variations found, select service directly
                        const serviceElement = document.querySelector(`[data-service-id="${serviceId}"]`);
                        if (serviceElement) {
                            selectService(serviceElement);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching variations:', error);
                    closeVariationSelectionModal();
                    // Fallback to direct selection
                    const serviceElement = document.querySelector(`[data-service-id="${serviceId}"]`);
                    if (serviceElement) {
                        selectService(serviceElement);
                    }
                });
        }

        function displayVariationOptions(service, variations) {
            const content = document.getElementById('variationSelectionContent');
            content.innerHTML = '';

            // Add service description if available
            if (service.description) {
                const description = document.createElement('div');
                description.className = 'mb-6 p-4 bg-secondary-800/50 rounded-lg border border-secondary-700';
                description.innerHTML = `<p class="text-gray-300">${escapeHtml(service.description)}</p>`;
                content.appendChild(description);
            }

            // Add variations
            variations.forEach(variation => {
                const variationDiv = document.createElement('div');
                variationDiv.className = 'border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300';
                variationDiv.onclick = () => {
                    closeVariationSelectionModal();
                    const serviceElement = document.querySelector(`[data-service-id="${service.id}"]`);
                    if (serviceElement) {
                        selectService(serviceElement, variation.id, variation);
                    }
                };

                variationDiv.innerHTML = `
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-lg font-semibold text-white">${escapeHtml(variation.name)}</h4>
                        <span class="text-2xl font-bold text-salon-gold"><?= $safeCurrencySymbol ?> ${parseFloat(variation.price).toLocaleString()}</span>
                    </div>
                    ${variation.description ? `<p class="text-sm text-gray-400 mb-4">${escapeHtml(variation.description)}</p>` : ''}
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-clock mr-2 text-salon-gold"></i>
                            ${variation.duration} minutes
                        </span>
                        <span class="text-salon-gold font-medium">Click to select</span>
                    </div>
                `;

                content.appendChild(variationDiv);
            });
        }

        function closeVariationSelectionModal() {
            document.getElementById('variationSelectionModal').classList.add('hidden');
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize service and package selection on page load
        initializeServiceSelection();
        initializePackageSelection();
        initializePaginationHandlers();

        // Initialize search and filter functionality
        function initializeSearchAndFilter() {
            const searchInput = document.getElementById('searchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const subcategoryFilter = document.getElementById('subcategoryFilter');
            const activeFilters = document.getElementById('activeFilters');
            const servicesSection = document.getElementById('servicesSection');
            const packagesSection = document.getElementById('packagesSection');

            // Load subcategories when category changes
            categoryFilter.addEventListener('change', async function() {
                const selectedCategory = this.value;
                
                // Reset subcategory filter
                subcategoryFilter.innerHTML = '<option value="">All Subcategories</option>';
                subcategoryFilter.disabled = true;
                
                if (selectedCategory) {
                    try {
                        const response = await fetch(`<?= getBasePath() ?>/api/customer/get-subcategories.php?category=${encodeURIComponent(selectedCategory)}`);
                        const data = await response.json();
                        
                        if (data.success && data.subcategories.length > 0) {
                            data.subcategories.forEach(subcategory => {
                                const option = document.createElement('option');
                                option.value = subcategory.name;
                                option.textContent = subcategory.name;
                                subcategoryFilter.appendChild(option);
                            });
                            subcategoryFilter.disabled = false;
                        }
                    } catch (error) {
                        console.error('Error loading subcategories:', error);
                    }
                }
                
                // Perform search with new category
                performSearch();
            });

            // Perform search when subcategory changes
            subcategoryFilter.addEventListener('change', performSearch);

            // Search functionality
            async function performSearch() {
                const searchTerm = searchInput.value.trim();
                const selectedCategory = categoryFilter.value;
                const selectedSubcategory = subcategoryFilter.value;

                // If no search terms, restore original pagination
                if (!searchTerm && !selectedCategory && !selectedSubcategory) {
                    restorePagination();
                    return;
                }

                // Show loading state
                servicesSection.style.opacity = '0.5';
                packagesSection.style.opacity = '0.5';

                try {
                    // Build query parameters
                    const params = new URLSearchParams();
                    if (searchTerm) params.append('search', searchTerm);
                    if (selectedCategory) params.append('category', selectedCategory);
                    if (selectedSubcategory) params.append('subcategory', selectedSubcategory);

                    // Fetch search results from API
                    const response = await fetch(`<?= getBasePath() ?>/api/customer/search-services.php?${params.toString()}`);
                    const data = await response.json();

                    if (data.success) {
                        // Update services section
                        const servicesHTML = data.services.map(service => {
                            const hasVariations = service.variations && service.variations.length > 0;
                            const priceDisplay = hasVariations ? 'Multiple Options' : `<?= $safeCurrencySymbol ?> ${parseFloat(service.price).toLocaleString()}`;
                            const durationDisplay = hasVariations ? 'Various durations' : `${service.duration} minutes`;

                            let variationsHTML = '';
                            if (hasVariations) {
                                variationsHTML = `
                                    <div class="mb-4 p-3 bg-secondary-800/30 rounded-lg border border-secondary-600">
                                        <p class="text-xs text-salon-gold font-medium mb-2">Available Options:</p>
                                        <div class="space-y-1">
                                            ${service.variations.map(variation => `
                                                <div class="flex justify-between text-xs text-gray-300">
                                                    <span>${variation.name}</span>
                                                    <span class="text-salon-gold"><?= $safeCurrencySymbol ?> ${parseFloat(variation.price).toLocaleString()}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                `;
                            }

                            return `
                                <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 hover-lift"
                                     data-service-id="${service.id}"
                                     data-service-price="${service.price}"
                                     data-service-duration="${service.duration}"
                                     data-has-variations="${hasVariations}"
                                     data-type="service">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-white text-lg">${service.name}</h4>
                                        <span class="text-salon-gold font-bold text-lg">${priceDisplay}</span>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-4 leading-relaxed">${service.description || ''}</p>
                                    ${variationsHTML}
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i>${durationDisplay}</span>
                                        <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i>${service.category_name || service.category || 'Uncategorized'}</span>
                                    </div>
                                </div>
                            `;
                        }).join('');

                        // Update packages section
                        const packagesHTML = data.packages.map(package => `
                            <div class="package-option border border-secondary-600 rounded-lg p-4 cursor-pointer hover:border-salon-gold transition-colors"
                                 data-package-id="${package.id}"
                                 data-package-price="${package.price}"
                                 data-package-duration="${package.total_duration}"
                                 data-type="package">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-white">${package.name}</h4>
                                    <div class="text-right">
                                        <span class="text-salon-gold font-semibold"><?= $safeCurrencySymbol ?> ${parseFloat(package.price).toLocaleString()}</span>
                                        ${package.savings > 0 ? `
                                            <div class="text-xs text-green-400">Save <?= $safeCurrencySymbol ?> ${parseFloat(package.savings).toLocaleString()}</div>
                                        ` : ''}
                                    </div>
                                </div>

                                ${package.description ? `
                                    <p class="text-sm text-gray-400 mb-2">${package.description}</p>
                                ` : ''}

                                <div class="mb-2">
                                    <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                    <div class="flex flex-wrap gap-1">
                                        ${package.services.map(service => `
                                            <span class="inline-block px-2 py-1 bg-secondary-700 text-xs rounded text-gray-300">
                                                ${service.name}
                                            </span>
                                        `).join('')}
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span><i class="fas fa-clock mr-1"></i>${package.total_duration} minutes total</span>
                                    <span><i class="fas fa-box mr-1"></i>${package.services.length} services</span>
                                </div>
                            </div>
                        `).join('');

                        // Update the DOM
                        const servicesContainer = servicesSection.querySelector('.grid');
                        const packagesContainer = packagesSection.querySelector('.grid');

                        servicesContainer.innerHTML = servicesHTML || '<div class="col-span-full text-center py-8 text-gray-400">No services found matching your search.</div>';
                        packagesContainer.innerHTML = packagesHTML || '<div class="col-span-full text-center py-8 text-gray-400">No packages found matching your search.</div>';

                        // Hide pagination when searching
                        hidePagination();

                        // Update active filters display
                        updateActiveFilters(searchTerm, selectedCategory, selectedSubcategory);

                        // Reinitialize click handlers
                        initializeServiceSelection();
                        initializePackageSelection();
                    } else {
                        showNotification('Error fetching search results');
                    }
                } catch (error) {
                    console.error('Search error:', error);
                    showNotification('Error performing search');
                } finally {
                    // Restore opacity
                    servicesSection.style.opacity = '1';
                    packagesSection.style.opacity = '1';
                }
            }

            // Function to restore original pagination
            async function restorePagination() {
                try {
                    // Get current page from URL
                    const currentPage = new URLSearchParams(window.location.search).get('page') || '1';

                    // Show loading state
                    servicesSection.style.opacity = '0.5';

                    // Fetch the current page content
                    const response = await fetch(`${window.location.pathname}?page=${currentPage}`);
                    const html = await response.text();

                    // Create a temporary container
                    const temp = document.createElement('div');
                    temp.innerHTML = html;

                    // Replace services section content
                    const newServicesSection = temp.querySelector('#servicesSection');
                    servicesSection.innerHTML = newServicesSection.innerHTML;

                    // Clear active filters
                    activeFilters.innerHTML = '';

                    // Show pagination controls
                    showPagination();

                    // Restore opacity
                    servicesSection.style.opacity = '1';

                    // Reinitialize service selection handlers
                    initializeServiceSelection();

                    // Reinitialize pagination handlers
                    initializePaginationHandlers();

                } catch (error) {
                    console.error('Error restoring pagination:', error);
                    // Fallback to page reload
                    const currentPage = new URLSearchParams(window.location.search).get('page') || '1';
                    window.location.href = `${window.location.pathname}?page=${currentPage}`;
                }
            }

            // Function to hide pagination controls
            function hidePagination() {
                const paginationControls = servicesSection.querySelector('.mt-8.flex.justify-center');
                if (paginationControls) {
                    paginationControls.style.display = 'none';
                }
            }

            // Function to show pagination controls
            function showPagination() {
                const paginationControls = servicesSection.querySelector('.mt-8.flex.justify-center');
                if (paginationControls) {
                    paginationControls.style.display = 'flex';
                }
            }

            // Update active filters display
            function updateActiveFilters(searchTerm, category, subcategory) {
                activeFilters.innerHTML = '';
                
                if (searchTerm) {
                    const searchFilter = createFilterTag(`Search: ${searchTerm}`, () => {
                        searchInput.value = '';
                        performSearch();
                    });
                    activeFilters.appendChild(searchFilter);
                }

                if (category) {
                    const categoryFilterTag = createFilterTag(`Category: ${category}`, () => {
                        categoryFilter.value = '';
                        subcategoryFilter.innerHTML = '<option value="">All Subcategories</option>';
                        subcategoryFilter.disabled = true;
                        performSearch();
                    });
                    activeFilters.appendChild(categoryFilterTag);
                }

                if (subcategory) {
                    const subcategoryFilterTag = createFilterTag(`Subcategory: ${subcategory}`, () => {
                        subcategoryFilter.value = '';
                        performSearch();
                    });
                    activeFilters.appendChild(subcategoryFilterTag);
                }
            }

            // Create filter tag element
            function createFilterTag(text, onRemove) {
                const tag = document.createElement('div');
                tag.className = 'inline-flex items-center bg-salon-gold/20 text-salon-gold px-3 py-1 rounded-full text-sm';
                tag.innerHTML = `
                    ${text}
                    <button class="ml-2 focus:outline-none" aria-label="Remove filter">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                tag.querySelector('button').addEventListener('click', onRemove);
                return tag;
            }

            // Event listeners
            searchInput.addEventListener('input', debounce(performSearch, 300));

            // Only perform search if there are active filters
            // Don't auto-search on page load to preserve pagination
            if (searchInput.value.trim() || categoryFilter.value || subcategoryFilter.value) {
                performSearch();
            }
        }

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Initialize search and filter when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeSearchAndFilter();
        });

        // Add these functions at the start of your script section
        function showNotification(message) {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notificationMessage');
            messageEl.textContent = message;
            
            // Show notification
            notification.classList.remove('translate-x-full', 'opacity-0');
            
            // Auto-hide after 3 seconds
            setTimeout(hideNotification, 3000);
        }

        function hideNotification() {
            const notification = document.getElementById('notification');
            notification.classList.add('translate-x-full', 'opacity-0');
        }
    </script>

<style>
/* Add these styles to your existing CSS */
.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0 0.75rem;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
}

.pagination-btn:hover {
    border-color: #f59e0b;
    color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
}

.pagination-btn.active {
    background-color: #f59e0b;
    border-color: #f59e0b;
    color: #000000;
}

/* Search and Filter Styles */
.service-option,
.package-option {
    transition: all 0.3s ease-in-out;
}

.service-option:not([style*="display: none"]),
.package-option:not([style*="display: none"]) {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Active Filter Tags */
#activeFilters > div {
    transition: all 0.2s ease-in-out;
}

#activeFilters > div:hover {
    background-color: rgba(245, 158, 11, 0.3);
}

#activeFilters button:hover i {
    transform: rotate(90deg);
    transition: transform 0.2s ease-in-out;
}

/* Search Input Focus Effect */
#searchInput:focus + div i {
    color: #f59e0b;
    transition: color 0.2s ease-in-out;
}

/* No Results Message Animation */
.no-results-message {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Grid Layout Improvements */
.grid {
    grid-auto-rows: minmax(min-content, max-content);
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
